# GitHub Pages Deployment - Personality Arena
# Repository: Taiwan-<PERSON>-<PERSON>/MVP_click_colour

# System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp

# Google Apps Script deployment (not needed for GitHub Pages)
gas-deployment/

# Development notes
notes.txt
todo.txt
NOTES.md
